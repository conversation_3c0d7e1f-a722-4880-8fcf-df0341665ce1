#!/usr/bin/env node

/**
 * Remove sensorData Section from Firebase
 * 
 * This script safely removes ONLY the "sensorData" section from Firebase
 * while preserving ALL other sections including:
 * - Sensor_Data (the newer categorized section)
 * - adminSession
 * - activeLocation
 * - contacts
 * - dataCollectionStatus
 * - locationHistory
 * - activeSensorDataCategory
 * 
 * Usage: node remove-sensor-data-section.js
 */

const { initializeFirebase } = require('./firebase');

async function removeSensorDataSection() {
  try {
    console.log('🚀 Starting Firebase sensorData section removal...');
    console.log('========================================');
    console.log('⚠️  WARNING: This will remove the "sensorData" section');
    console.log('✅ Other sections will be preserved:');
    console.log('   - Sensor_Data (categorized data)');
    console.log('   - adminSession');
    console.log('   - activeLocation');
    console.log('   - contacts');
    console.log('   - dataCollectionStatus');
    console.log('   - locationHistory');
    console.log('   - activeSensorDataCategory');
    console.log('========================================\n');
    
    // Initialize Firebase
    const { database } = initializeFirebase();
    
    // First, let's check what exists in the database root
    console.log('🔍 Checking Firebase database structure...');
    const rootRef = database.ref('/');
    const rootSnapshot = await rootRef.once('value');
    
    if (!rootSnapshot.exists()) {
      console.log('❌ Firebase database appears to be empty');
      return { success: false, message: 'Database is empty' };
    }
    
    const rootData = rootSnapshot.val();
    const allSections = Object.keys(rootData);
    
    console.log('📊 Current Firebase sections:');
    allSections.forEach(section => {
      console.log(`   📁 ${section}`);
    });
    console.log('');
    
    // Check if sensorData section exists
    if (!allSections.includes('sensorData')) {
      console.log('ℹ️ sensorData section does not exist or is already empty');
      console.log('✅ Nothing to remove');
      return { success: true, message: 'sensorData section was already empty' };
    }
    
    // Analyze sensorData section
    console.log('🔍 Analyzing sensorData section...');
    const sensorDataRef = database.ref('sensorData');
    const sensorDataSnapshot = await sensorDataRef.once('value');
    const sensorData = sensorDataSnapshot.val();
    const locations = Object.keys(sensorData);
    
    console.log(`📍 Found locations in sensorData: ${locations.join(', ')}`);
    
    // Count total entries for confirmation
    let totalEntries = 0;
    let locationSummary = [];
    
    for (const location of locations) {
      const locationData = sensorData[location];
      if (locationData && typeof locationData === 'object') {
        const sensorReadings = Object.keys(locationData).filter(key => key !== 'dailyAverage');
        const dailyAverages = locationData.dailyAverage ? Object.keys(locationData.dailyAverage).length : 0;
        const locationTotal = sensorReadings.length + dailyAverages;
        totalEntries += locationTotal;
        
        locationSummary.push({
          location,
          sensorReadings: sensorReadings.length,
          dailyAverages,
          total: locationTotal
        });
        
        console.log(`   📊 ${location}: ${sensorReadings.length} sensor readings, ${dailyAverages} daily averages`);
      }
    }
    
    console.log(`\n📈 Total entries to be removed: ${totalEntries}`);
    console.log('');
    
    // Final confirmation
    console.log('🚨 FINAL CONFIRMATION:');
    console.log(`   🗑️  Will remove: "sensorData" section (${totalEntries} entries)`);
    console.log('   ✅ Will preserve: All other sections');
    console.log('   ⚠️  This action CANNOT be undone!');
    console.log('');
    
    // Wait a moment for user to see the information
    console.log('⏳ Proceeding with removal in 3 seconds...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Perform the removal
    console.log('🗑️ Removing sensorData section...');
    await sensorDataRef.remove();
    
    // Verify removal
    console.log('🔍 Verifying removal...');
    const verifySnapshot = await sensorDataRef.once('value');
    
    if (!verifySnapshot.exists()) {
      console.log('✅ SUCCESS: sensorData section successfully removed from Firebase');
      
      // Show remaining sections
      const finalRootSnapshot = await rootRef.once('value');
      const finalSections = Object.keys(finalRootSnapshot.val() || {});
      
      console.log('\n📊 Remaining Firebase sections:');
      finalSections.forEach(section => {
        console.log(`   📁 ${section}`);
      });
      
      console.log('\n🎉 Operation completed successfully!');
      console.log('📋 Summary:');
      console.log(`   🗑️  Removed: sensorData section`);
      console.log(`   📊 Total entries removed: ${totalEntries}`);
      console.log(`   📍 Locations removed: ${locations.join(', ')}`);
      console.log(`   ✅ Preserved sections: ${finalSections.length}`);
      
      return {
        success: true,
        message: 'sensorData section successfully removed',
        removedLocations: locations,
        totalEntriesRemoved: totalEntries,
        locationSummary,
        preservedSections: finalSections
      };
    } else {
      throw new Error('Verification failed: sensorData section still exists');
    }
    
  } catch (error) {
    console.error('❌ Error removing sensorData section:', error);
    console.error('🔧 Stack trace:', error.stack);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  removeSensorDataSection()
    .then(result => {
      console.log('\n✅ Script completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { removeSensorDataSection };
